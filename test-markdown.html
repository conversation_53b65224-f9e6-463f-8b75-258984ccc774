<!DOCTYPE html>
<html>
<head>
    <title>Markdown Test</title>
</head>
<body>
    <div id="test">**bold text**</div>
    <script>
        // Test the regex patterns
        const patterns = [
            { regex: /(\*\*\*)(.*?)\1/, class: 'markdown-bold-italic', tag: 'strong' },
            { regex: /(___)(.*?)\1/, class: 'markdown-bold-italic', tag: 'strong' },
            { regex: /(\*\*)(.*?)\1/, class: 'markdown-bold', tag: 'strong' },
            { regex: /(__)(.*?)\1/, class: 'markdown-underline', tag: 'u' },
            { regex: /(\*)(.*?)\1/, class: 'markdown-italic', tag: 'em' },
            { regex: /(_)(.*?)\1/, class: 'markdown-italic', tag: 'em' },
            { regex: /(~~)(.*?)\1/, class: 'markdown-strikethrough', tag: 'del' },
            { regex: /(`)(.*?)\1/, class: 'markdown-code', tag: 'code' }
        ];

        const testText = "**bold text**";
        console.log('Testing:', testText);
        
        for (const pattern of patterns) {
            const regex = new RegExp(pattern.regex.source, 'g');
            const match = regex.exec(testText);
            if (match) {
                console.log('Pattern matched:', pattern.class, match);
                const result = testText.replace(regex, (match, delimiters, content) => {
                    return `<${pattern.tag} class="${pattern.class}">${delimiters}${content}${delimiters}</${pattern.tag}>`;
                });
                console.log('Result:', result);
                break;
            }
        }
    </script>
</body>
</html>
